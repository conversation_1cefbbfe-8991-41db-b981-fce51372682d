{"version": 3, "file": "intercom.js", "sourceRoot": "", "sources": ["../../src/services/intercom.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAmE,MAAM,OAAO,CAAC;AACxF,OAAO,EAAG,eAAe,EAAE,MAAM,KAAK,CAAC;AAQvC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,GAAG,EAAE,MAAM,oBAAoB,CAAC;AACzC,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAW3D,MAAM,OAAO,iBAAiB;IACpB,MAAM,CAAgB;IACtB,aAAa,GAAkB;QACrC,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;KACpC,CAAC;IAEF;QACE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO;YAChC,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE;gBACxD,QAAQ,EAAE,kBAAkB;gBAC5B,cAAc,EAAE,kBAAkB;gBAClC,kBAAkB,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU;aAC/C;YACD,OAAO,EAAE,KAAK,EAAE,aAAa;SAC9B,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE;YACT,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;YACxE,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,yDAAyD;QACzD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,EAAE;YACX,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACnC,GAAG,CAAC,WAAW,CACb,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,KAAK,EAC9C,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,EACzB,QAAQ,CAAC,MAAM,EACf,IAAI,CAAC,GAAG,EAAE,GAAG,CAAE,QAAQ,CAAC,MAAqC,CAAC,QAAQ,EAAE,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CACjG,CAAC;YACF,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACzC,GAAG,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBAC9B,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM;oBAC7B,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;iBAC1B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,CAAC,CACF,CAAC;QAEF,8BAA8B;QAC9B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7C,MAAqC,CAAC,QAAQ,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC5E,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,QAAuB;QACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACjC,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,OAAO,CAAC,uBAAuB,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC;QAChF,CAAC;QACD,IAAI,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,GAAG,CAAC,SAAS,CACX,UAAU,EACV,IAAI,CAAC,aAAa,CAAC,SAAS,EAC5B,IAAI,CAAC,aAAa,CAAC,SAAS,CAC7B,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,KAAU;QAC/B,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACjC,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAwB,CAAC;YAC9D,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM;iBACtC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,OAAO,IAAI,KAAK,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;YACnC,OAAO,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;YACnC,OAAO,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;YACnC,OAAO,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAEO,cAAc;QACpB,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAA2B,EAAE;QACnD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC;QAC1C,IAAI,MAAM,CAAC,QAAQ;YAAE,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChF,IAAI,MAAM,CAAC,cAAc;YAAE,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;QAEvF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACpC,kBAAkB,WAAW,CAAC,QAAQ,EAAE,EAAE,CAC3C,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,cAAsB;QAC1C,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACpC,kBAAkB,cAAc,EAAE,CACnC,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,KAA0B;QAClD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,uBAAuB,EACvB,KAAK,CACN,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,SAM1B,EAAE;QACJ,MAAM,KAAK,GAAwB;YACjC,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,4BAA4B;wBACnC,QAAQ,EAAE,GAAG;wBACb,KAAK,EAAE,MAAM;qBACd;iBACF;aACF;SACF,CAAC;QAEF,wBAAwB;QACxB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACxB,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;iBACzE,CAAC,CAAC;YACL,CAAC;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC7B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACxB,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;iBACvE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;aACjC,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxB,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,OAAe,EACf,SAGI,EAAE;QAEN,MAAM,KAAK,GAAwB;YACjC,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,mBAAmB;wBAC1B,QAAQ,EAAE,GAAG;wBACb,KAAK,EAAE,OAAO;qBACf;iBACF;aACF;SACF,CAAC;QAEF,wBAAwB;QACxB,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACxB,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;iBACzE,CAAC,CAAC;YACL,CAAC;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC7B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACxB,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;iBACvE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAA4C,SAAS,CAAC,CAAC;QAC7F,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAe;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAgB,WAAW,OAAO,EAAE,CAAC,CAAC;QAC5E,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IACnC,CAAC;CACF"}