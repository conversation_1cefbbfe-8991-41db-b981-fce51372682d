/**
 * Intercom API client with rate limiting and error handling
 */
import type { IntercomConversation, IntercomConversationList, IntercomSearchQuery, IntercomAdmin, RateLimitInfo, PaginationParams } from '../types/index.js';
export declare class IntercomApiClient {
    private client;
    private rateLimitInfo;
    constructor();
    private updateRateLimitInfo;
    private handleApiError;
    private checkRateLimit;
    /**
     * List all conversations with optional filtering
     */
    listConversations(params?: PaginationParams): Promise<IntercomConversationList>;
    /**
     * Get a specific conversation by ID
     */
    getConversation(conversationId: string): Promise<IntercomConversation>;
    /**
     * Search conversations using Intercom's search API
     */
    searchConversations(query: IntercomSearchQuery): Promise<IntercomConversationList>;
    /**
     * Get conversations without ratings
     */
    getUnratedConversations(params?: {
        dateRange?: {
            startDate: string;
            endDate: string;
        };
        adminIds?: string[];
        status?: ('open' | 'closed' | 'snoozed')[];
        priority?: ('high' | 'normal' | 'low')[];
        pagination?: PaginationParams;
    }): Promise<IntercomConversationList>;
    /**
     * Get conversations by admin ID
     */
    getConversationsByAdmin(adminId: string, params?: {
        dateRange?: {
            startDate: string;
            endDate: string;
        };
        pagination?: PaginationParams;
    }): Promise<IntercomConversationList>;
    /**
     * Get all admins
     */
    getAdmins(): Promise<IntercomAdmin[]>;
    /**
     * Get admin by ID
     */
    getAdmin(adminId: string): Promise<IntercomAdmin>;
    /**
     * Get current rate limit information
     */
    getRateLimitInfo(): RateLimitInfo;
}
//# sourceMappingURL=intercom.d.ts.map