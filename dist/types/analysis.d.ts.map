{"version": 3, "file": "analysis.d.ts", "sourceRoot": "", "sources": ["../../src/types/analysis.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,MAAM,WAAW,2BAA2B;IAC1C,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE;QACV,cAAc,EAAE,gBAAgB,CAAC;QACjC,WAAW,EAAE,gBAAgB,CAAC;QAC9B,eAAe,EAAE,gBAAgB,CAAC;QAClC,iBAAiB,EAAE,gBAAgB,CAAC;QACpC,oBAAoB,EAAE,gBAAgB,CAAC;KACxC,CAAC;IACF,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,iBAAiB,EAAE,MAAM,CAAC;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACrB;AAED,MAAM,WAAW,0BAA0B;IACzC,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE;QACN,SAAS,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,OAAO,EAAE;QACP,kBAAkB,EAAE,MAAM,CAAC;QAC3B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,qBAAqB,EAAE,MAAM,CAAC;QAC9B,yBAAyB,EAAE,MAAM,CAAC;QAClC,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,mBAAmB,EAAE,MAAM,EAAE,CAAC;IAC9B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,oBAAoB,EAAE;QACpB,IAAI,EAAE,mBAAmB,EAAE,CAAC;QAC5B,gBAAgB,EAAE,mBAAmB,EAAE,CAAC;KACzC,CAAC;CACH;AAED,MAAM,WAAW,mBAAmB;IAClC,cAAc,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B;AAED,MAAM,WAAW,oBAAoB;IACnC,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE;QACR,QAAQ,EAAE,MAAM,CAAC;QACjB,YAAY,EAAE,MAAM,CAAC;QACrB,gBAAgB,EAAE,MAAM,CAAC;QACzB,IAAI,EAAE,MAAM,EAAE,CAAC;QACf,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;KACrC,CAAC;IACF,SAAS,EAAE;QACT,OAAO,EAAE,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC;QAC7C,WAAW,EAAE,KAAK,CAAC;YACjB,KAAK,EAAE,SAAS,GAAG,QAAQ,GAAG,YAAY,CAAC;YAC3C,SAAS,EAAE,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC;YAC/C,UAAU,EAAE,MAAM,CAAC;SACpB,CAAC,CAAC;KACJ,CAAC;IACF,MAAM,EAAE,KAAK,CAAC;QACZ,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC,CAAC;IACH,UAAU,EAAE;QACV,MAAM,EAAE,UAAU,GAAG,YAAY,GAAG,WAAW,CAAC;QAChD,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QACtC,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,kBAAkB,EAAE;QAClB,sBAAsB,EAAE,MAAM,EAAE,CAAC;QACjC,qBAAqB,EAAE,MAAM,EAAE,CAAC;QAChC,eAAe,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;KAC5C,CAAC;IACF,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,cAAc,EAAE,OAAO,CAAC;CACzB;AAED,MAAM,WAAW,kBAAkB;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,2BAA2B,EAAE,CAAC;IACvC,MAAM,EAAE,KAAK,CAAC;QACZ,cAAc,EAAE,MAAM,CAAC;QACvB,KAAK,EAAE,MAAM,CAAC;KACf,CAAC,CAAC;IACH,OAAO,EAAE;QACP,mBAAmB,EAAE,MAAM,CAAC;QAC5B,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,aAAa,EAAE,MAAM,EAAE,CAAC;QACxB,kBAAkB,EAAE,MAAM,EAAE,CAAC;KAC9B,CAAC;CACH;AAED,MAAM,WAAW,mBAAmB;IAClC,cAAc,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE;QACd,EAAE,EAAE,MAAM,CAAC;QACX,IAAI,EAAE,MAAM,CAAC;KACd,CAAC;IACF,YAAY,EAAE;QACZ,EAAE,EAAE,MAAM,CAAC;QACX,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC;IACtC,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACpC,IAAI,EAAE,MAAM,EAAE,CAAC;IACf,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,CAAC,EAAE;QACV,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAC/B,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;KAC9B,CAAC;IACF,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC;IACzC,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC;IAC3C,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,MAAM,WAAW,cAAc;IAC7B,wBAAwB,EAAE,OAAO,CAAC;IAClC,uBAAuB,EAAE,OAAO,CAAC;IACjC,aAAa,EAAE,OAAO,GAAG,UAAU,GAAG,eAAe,CAAC;IACtD,UAAU,CAAC,EAAE,CAAC,gBAAgB,GAAG,aAAa,GAAG,iBAAiB,GAAG,YAAY,CAAC,EAAE,GAAG,SAAS,CAAC;CAClG;AAED,MAAM,MAAM,cAAc,GAAG,SAAS,GAAG,aAAa,GAAG,WAAW,GAAG,QAAQ,CAAC;AAEhF,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC;IACxC,MAAM,EAAE,cAAc,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,EAAE,cAAc,CAAC;IACvB,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,OAAO,CAAC,EAAE,2BAA2B,GAAG,kBAAkB,GAAG,0BAA0B,CAAC;IACxF,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB"}