{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../src/server.ts"], "names": [], "mappings": ";AAEA;;;;;GAKG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,yCAAyC,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EACL,aAAa,EACb,uBAAuB,EACvB,yBAAyB,EACzB,6BAA6B,EAC7B,+BAA+B,EAC/B,6BAA6B,EAC7B,8BAA8B,GAC/B,MAAM,uBAAuB,CAAC;AAI/B,MAAM,iBAAiB;IACb,MAAM,CAAY;IAClB,eAAe,CAAkB;IAEzC;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;YAC1B,IAAI,EAAE,0BAA0B;YAChC,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,4CAA4C;QAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,oBAAoB,EACpB;YACE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;gBAClB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBAChC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aAC/B,CAAC,CAAC,QAAQ,EAAE;YACb,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;YACxC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;YACpC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YACjE,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;YACjC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC;gBACnB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACxC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACrD,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aACtC,CAAC,CAAC,QAAQ,EAAE;SACd,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;gBAEvE,mDAAmD;gBACnD,MAAM,MAAM,GAA4B,EAAE,CAAC;gBAC3C,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;oBAC9B,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;gBAC/C,CAAC;gBACD,IAAI,eAAe,CAAC,QAAQ;oBAAE,MAAM,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBACzE,IAAI,eAAe,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;gBAC7D,IAAI,eAAe,CAAC,MAAM;oBAAE,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;gBACnE,IAAI,eAAe,CAAC,SAAS,KAAK,SAAS;oBAAE,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;gBAE1F,uDAAuD;gBACvD,MAAM,UAAU,GAA8B,EAAE,CAAC;gBACjD,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI;oBAAE,UAAU,CAAC,IAAI,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;gBACxF,IAAI,eAAe,CAAC,UAAU,EAAE,QAAQ;oBAAE,UAAU,CAAC,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACpG,IAAI,eAAe,CAAC,UAAU,EAAE,cAAc;oBAAE,UAAU,CAAC,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC;gBAEtH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CACzD,MAAM,EACN,UAAU,CACX,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oCAC/C,EAAE,EAAE,IAAI,CAAC,EAAE;oCACX,KAAK,EAAE,IAAI,CAAC,KAAK;oCACjB,MAAM,EAAE,IAAI,CAAC,KAAK;oCAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oCACvB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;oCACzD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;oCACzD,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB;oCACrC,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE,MAAM;oCACxC,eAAe,EAAE,IAAI,CAAC,iBAAiB;oCACvC,YAAY,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,IAAI,CAAC;oCACvD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;iCAC1C,CAAC,CAAC;gCACH,UAAU,EAAE,MAAM,CAAC,UAAU;6BAC9B,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,EAAE,KAAc,CAAC,CAAC;gBAChE,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,UAAW,KAAe,CAAC,OAAO,EAAE;yBAC3C,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,8BAA8B,EAC9B;YACE,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACjC,wBAAwB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACnD,uBAAuB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClD,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;YACjF,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;SAC3G,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;gBAEzE,mDAAmD;gBACnD,MAAM,MAAM,GAA4B;oBACtC,wBAAwB,EAAE,eAAe,CAAC,wBAAwB,IAAI,IAAI;oBAC1E,uBAAuB,EAAE,eAAe,CAAC,uBAAuB,IAAI,IAAI;oBACxE,aAAa,EAAE,eAAe,CAAC,aAAa,IAAI,UAAU;iBAC3D,CAAC;gBACF,IAAI,eAAe,CAAC,UAAU;oBAAE,MAAM,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;gBAE/E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,0BAA0B,CACpE,eAAe,CAAC,cAAc,EAC9B,MAAwB,CACzB,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;yBACxC,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,0CAA0C,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,KAAc,CAAC,CAAC;gBACjH,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,iCAAkC,KAAe,CAAC,OAAO,EAAE;yBAClE,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2BAA2B,EAC3B;YACE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;gBAClB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBAChC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aAC/B,CAAC,CAAC,QAAQ,EAAE;YACb,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;YACxC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YACjE,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YAC/D,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC;gBACnB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACxC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACrD,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aACtC,CAAC,CAAC,QAAQ,EAAE;YACb,4BAA4B,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;SACzD,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;gBAE7E,mDAAmD;gBACnD,MAAM,MAAM,GAA4B,EAAE,CAAC;gBAC3C,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;oBAC9B,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;gBAC/C,CAAC;gBACD,IAAI,eAAe,CAAC,QAAQ;oBAAE,MAAM,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBACzE,IAAI,eAAe,CAAC,MAAM;oBAAE,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;gBACnE,IAAI,eAAe,CAAC,QAAQ;oBAAE,MAAM,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBAEzE,uDAAuD;gBACvD,MAAM,UAAU,GAA8B,EAAE,CAAC;gBACjD,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI;oBAAE,UAAU,CAAC,IAAI,GAAG,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;gBACxF,IAAI,eAAe,CAAC,UAAU,EAAE,QAAQ;oBAAE,UAAU,CAAC,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACpG,IAAI,eAAe,CAAC,UAAU,EAAE,cAAc;oBAAE,UAAU,CAAC,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC,cAAc,CAAC;gBAEtH,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAC7E,MAAM,EACN,UAAU,CACX,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,oBAAoB;gCACpB,KAAK,EAAE,oBAAoB,CAAC,MAAM;gCAClC,OAAO,EAAE;oCACP,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,QAAQ,CAAC;oCACtD,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,UAAU,CAAC;oCAC1D,mBAAmB,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,oBAAoB,CAAC,MAAM,IAAI,CAAC;iCAC/H;6BACF,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,EAAE,KAAc,CAAC,CAAC;gBACvE,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,yCAA0C,KAAe,CAAC,OAAO,EAAE;yBAC1E,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,sCAAsC;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,6BAA6B,EAC7B;YACE,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;gBAClB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;gBAChC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aAC/B,CAAC;YACF,2BAA2B,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACtD,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;SACxD,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,+BAA+B,EAAE,MAAM,CAAC,CAAC;gBAC/E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,yBAAyB,CACtE,eAAe,CAAC,OAAO,EACvB,eAAe,CAAC,SAAS,EACzB;oBACE,2BAA2B,EAAE,eAAe,CAAC,2BAA2B,IAAI,IAAI;oBAChF,WAAW,EAAE,eAAe,CAAC,WAAW,IAAI,CAAC;iBAC9C,CACF,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;yBAC3C,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,KAAc,CAAC,CAAC;gBAClG,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,wCAAyC,KAAe,CAAC,OAAO,EAAE;yBACzE,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,oCAAoC;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2BAA2B,EAC3B;YACE,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACjC,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACxC,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAC3C,kBAAkB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;SAC9C,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;gBAC7E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,uBAAuB,CACjE,eAAe,CAAC,cAAc,EAC9B;oBACE,aAAa,EAAE,eAAe,CAAC,aAAa,IAAI,IAAI;oBACpD,gBAAgB,EAAE,eAAe,CAAC,gBAAgB,IAAI,IAAI;oBAC1D,kBAAkB,EAAE,eAAe,CAAC,kBAAkB,IAAI,IAAI;iBAC/D,CACF,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;yBACxC,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,KAAc,CAAC,CAAC;gBAC9G,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,wCAAyC,KAAe,CAAC,OAAO,EAAE;yBACzE,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,qCAAqC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,4BAA4B,EAC5B;YACE,eAAe,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;YACjF,wBAAwB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACnD,uBAAuB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClD,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YAC1G,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;SAC3C,EACD,KAAK,EAAE,MAAM,EAAE,EAAE;YACf,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,aAAa,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;gBAE9E,mDAAmD;gBACnD,MAAM,MAAM,GAA4B;oBACtC,wBAAwB,EAAE,eAAe,CAAC,wBAAwB,IAAI,IAAI;oBAC1E,uBAAuB,EAAE,eAAe,CAAC,uBAAuB,IAAI,IAAI;oBACxE,aAAa,EAAE,eAAe,CAAC,aAAa,IAAI,UAAU;iBAC3D,CAAC;gBACF,IAAI,eAAe,CAAC,UAAU;oBAAE,MAAM,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;gBAE/E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,wBAAwB,CACpE,eAAe,CAAC,eAAe,EAC/B,MAAwB,CACzB,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;yBAC1C,CAAC;iBACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,eAAe,EAAE,EAAE,KAAc,CAAC,CAAC;gBACjH,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,mCAAoC,KAAe,CAAC,OAAO,EAAE;yBACpE,CAAC;oBACF,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,OAAO,CAAI,KAAU,EAAE,GAAY;QACzC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACnC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,EAA4B,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,GAAG,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACvC,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO;gBAC9B,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ;aACjC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAErC,GAAG,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,EAAE,KAAc,CAAC,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AAED,gDAAgD;AAChD,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAC;IACvC,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC"}