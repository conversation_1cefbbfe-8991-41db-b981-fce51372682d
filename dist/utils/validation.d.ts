/**
 * Input validation utilities using Zod schemas
 */
import { z } from 'zod';
export declare const conversationIdSchema: z.ZodString;
export declare const dateRangeSchema: z.ZodEffects<z.ZodObject<{
    startDate: z.ZodString;
    endDate: z.ZodString;
}, "strip", z.ZodTypeAny, {
    startDate: string;
    endDate: string;
}, {
    startDate: string;
    endDate: string;
}>, {
    startDate: string;
    endDate: string;
}, {
    startDate: string;
    endDate: string;
}>;
export declare const flexibleDateRangeSchema: z.ZodEffects<z.ZodOptional<z.ZodEffects<z.ZodObject<{
    startDate: z.ZodOptional<z.ZodString>;
    endDate: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    startDate?: string | undefined;
    endDate?: string | undefined;
}, {
    startDate?: string | undefined;
    endDate?: string | undefined;
}>, {
    startDate?: string | undefined;
    endDate?: string | undefined;
}, {
    startDate?: string | undefined;
    endDate?: string | undefined;
}>>, {
    startDate?: string | undefined;
    endDate?: string | undefined;
} | undefined, unknown>;
export declare const paginationSchema: z.ZodObject<{
    page: z.ZodOptional<z.ZodNumber>;
    per_page: z.ZodOptional<z.ZodNumber>;
    starting_after: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    page?: number | undefined;
    per_page?: number | undefined;
    starting_after?: string | undefined;
}, {
    page?: number | undefined;
    per_page?: number | undefined;
    starting_after?: string | undefined;
}>;
export declare const sortSchema: z.ZodObject<{
    field: z.ZodString;
    order: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    field: string;
    order: "asc" | "desc";
}, {
    field: string;
    order?: "asc" | "desc" | undefined;
}>;
export declare const listConversationsSchema: z.ZodObject<{
    dateRange: z.ZodOptional<z.ZodEffects<z.ZodOptional<z.ZodEffects<z.ZodObject<{
        startDate: z.ZodOptional<z.ZodString>;
        endDate: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>>, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    } | undefined, unknown>>;
    adminIds: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    status: z.ZodOptional<z.ZodArray<z.ZodEnum<["open", "closed", "snoozed"]>, "many">>;
    hasRating: z.ZodOptional<z.ZodBoolean>;
    pagination: z.ZodOptional<z.ZodObject<{
        page: z.ZodOptional<z.ZodNumber>;
        per_page: z.ZodOptional<z.ZodNumber>;
        starting_after: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    }, {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    }>>;
    sort: z.ZodOptional<z.ZodObject<{
        field: z.ZodString;
        order: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
    }, "strip", z.ZodTypeAny, {
        field: string;
        order: "asc" | "desc";
    }, {
        field: string;
        order?: "asc" | "desc" | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    sort?: {
        field: string;
        order: "asc" | "desc";
    } | undefined;
    status?: ("open" | "closed" | "snoozed")[] | undefined;
    dateRange?: {
        startDate?: string | undefined;
        endDate?: string | undefined;
    } | undefined;
    adminIds?: string[] | undefined;
    tags?: string[] | undefined;
    hasRating?: boolean | undefined;
    pagination?: {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    } | undefined;
}, {
    sort?: {
        field: string;
        order?: "asc" | "desc" | undefined;
    } | undefined;
    status?: ("open" | "closed" | "snoozed")[] | undefined;
    dateRange?: unknown;
    adminIds?: string[] | undefined;
    tags?: string[] | undefined;
    hasRating?: boolean | undefined;
    pagination?: {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    } | undefined;
}>;
export declare const analyzeConversationSchema: z.ZodObject<{
    conversationId: z.ZodString;
    includeConversationParts: z.ZodDefault<z.ZodBoolean>;
    includeCustomerFeedback: z.ZodDefault<z.ZodBoolean>;
    analysisDepth: z.ZodDefault<z.ZodEnum<["basic", "detailed", "comprehensive"]>>;
    focusAreas: z.ZodOptional<z.ZodArray<z.ZodEnum<["responsiveness", "helpfulness", "professionalism", "resolution"]>, "many">>;
}, "strip", z.ZodTypeAny, {
    includeConversationParts: boolean;
    includeCustomerFeedback: boolean;
    conversationId: string;
    analysisDepth: "basic" | "detailed" | "comprehensive";
    focusAreas?: ("resolution" | "responsiveness" | "helpfulness" | "professionalism")[] | undefined;
}, {
    conversationId: string;
    includeConversationParts?: boolean | undefined;
    includeCustomerFeedback?: boolean | undefined;
    analysisDepth?: "basic" | "detailed" | "comprehensive" | undefined;
    focusAreas?: ("resolution" | "responsiveness" | "helpfulness" | "professionalism")[] | undefined;
}>;
export declare const getUnratedConversationsSchema: z.ZodObject<{
    dateRange: z.ZodOptional<z.ZodEffects<z.ZodOptional<z.ZodEffects<z.ZodObject<{
        startDate: z.ZodOptional<z.ZodString>;
        endDate: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    }>>, {
        startDate?: string | undefined;
        endDate?: string | undefined;
    } | undefined, unknown>>;
    adminIds: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    status: z.ZodOptional<z.ZodArray<z.ZodEnum<["open", "closed", "snoozed"]>, "many">>;
    priority: z.ZodOptional<z.ZodArray<z.ZodEnum<["high", "normal", "low"]>, "many">>;
    pagination: z.ZodOptional<z.ZodObject<{
        page: z.ZodOptional<z.ZodNumber>;
        per_page: z.ZodOptional<z.ZodNumber>;
        starting_after: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    }, {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    }>>;
    sort: z.ZodOptional<z.ZodObject<{
        field: z.ZodString;
        order: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
    }, "strip", z.ZodTypeAny, {
        field: string;
        order: "asc" | "desc";
    }, {
        field: string;
        order?: "asc" | "desc" | undefined;
    }>>;
    includePredictedSatisfaction: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    includePredictedSatisfaction: boolean;
    sort?: {
        field: string;
        order: "asc" | "desc";
    } | undefined;
    status?: ("open" | "closed" | "snoozed")[] | undefined;
    priority?: ("high" | "normal" | "low")[] | undefined;
    dateRange?: {
        startDate?: string | undefined;
        endDate?: string | undefined;
    } | undefined;
    adminIds?: string[] | undefined;
    pagination?: {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    } | undefined;
}, {
    sort?: {
        field: string;
        order?: "asc" | "desc" | undefined;
    } | undefined;
    status?: ("open" | "closed" | "snoozed")[] | undefined;
    priority?: ("high" | "normal" | "low")[] | undefined;
    dateRange?: unknown;
    adminIds?: string[] | undefined;
    pagination?: {
        page?: number | undefined;
        per_page?: number | undefined;
        starting_after?: string | undefined;
    } | undefined;
    includePredictedSatisfaction?: boolean | undefined;
}>;
export declare const analyzeSupportPerformanceSchema: z.ZodObject<{
    adminId: z.ZodString;
    dateRange: z.ZodEffects<z.ZodObject<{
        startDate: z.ZodString;
        endDate: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        startDate: string;
        endDate: string;
    }, {
        startDate: string;
        endDate: string;
    }>, {
        startDate: string;
        endDate: string;
    }, {
        startDate: string;
        endDate: string;
    }>;
    includeConversationExamples: z.ZodDefault<z.ZodBoolean>;
    maxExamples: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    dateRange: {
        startDate: string;
        endDate: string;
    };
    adminId: string;
    includeConversationExamples: boolean;
    maxExamples: number;
}, {
    dateRange: {
        startDate: string;
        endDate: string;
    };
    adminId: string;
    includeConversationExamples?: boolean | undefined;
    maxExamples?: number | undefined;
}>;
export declare const getConversationInsightsSchema: z.ZodObject<{
    conversationId: z.ZodString;
    includeTopics: z.ZodDefault<z.ZodBoolean>;
    includeSentiment: z.ZodDefault<z.ZodBoolean>;
    includeActionItems: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    conversationId: string;
    includeTopics: boolean;
    includeSentiment: boolean;
    includeActionItems: boolean;
}, {
    conversationId: string;
    includeTopics?: boolean | undefined;
    includeSentiment?: boolean | undefined;
    includeActionItems?: boolean | undefined;
}>;
export declare const bulkAnalyzeConversationsSchema: z.ZodObject<{
    conversationIds: z.ZodArray<z.ZodString, "many">;
    analysisDepth: z.ZodDefault<z.ZodEnum<["basic", "detailed", "comprehensive"]>>;
    includeConversationParts: z.ZodDefault<z.ZodBoolean>;
    includeCustomerFeedback: z.ZodDefault<z.ZodBoolean>;
    focusAreas: z.ZodOptional<z.ZodArray<z.ZodEnum<["responsiveness", "helpfulness", "professionalism", "resolution"]>, "many">>;
    generateSummary: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    includeConversationParts: boolean;
    includeCustomerFeedback: boolean;
    analysisDepth: "basic" | "detailed" | "comprehensive";
    conversationIds: string[];
    generateSummary: boolean;
    focusAreas?: ("resolution" | "responsiveness" | "helpfulness" | "professionalism")[] | undefined;
}, {
    conversationIds: string[];
    includeConversationParts?: boolean | undefined;
    includeCustomerFeedback?: boolean | undefined;
    analysisDepth?: "basic" | "detailed" | "comprehensive" | undefined;
    focusAreas?: ("resolution" | "responsiveness" | "helpfulness" | "professionalism")[] | undefined;
    generateSummary?: boolean | undefined;
}>;
export declare function validateInput<T>(schema: z.ZodSchema<T>, input: unknown): T;
export declare function validateConversationId(id: unknown): string;
export declare function validateDateRange(range: unknown): {
    startDate: string;
    endDate: string;
};
export declare function validatePagination(params: unknown): z.infer<typeof paginationSchema>;
export declare function isValidEmail(email: string): boolean;
export declare function isValidUrl(url: string): boolean;
export declare function sanitizeString(input: string): string;
export declare function normalizeTag(tag: string): string;
export declare function validateRateLimit(remaining: number, resetTime: number): void;
export declare function sanitizeForAnalysis(text: string): string;
export declare function validateConversationContent(content: string): boolean;
//# sourceMappingURL=validation.d.ts.map