/**
 * Input validation utilities using Zod schemas
 */
import { z } from 'zod';
// Common validation schemas
export const conversationIdSchema = z.string().min(1, 'Conversation ID is required');
export const dateRangeSchema = z.object({
    startDate: z.string().datetime('Invalid start date format'),
    endDate: z.string().datetime('Invalid end date format'),
}).refine((data) => new Date(data.startDate) <= new Date(data.endDate), {
    message: 'Start date must be before or equal to end date',
    path: ['startDate'],
});
// More flexible date range schema that allows undefined values
export const flexibleDateRangeSchema = z.preprocess((data) => {
    // If data is not an object or is null, return undefined
    if (!data || typeof data !== 'object') {
        return undefined;
    }
    const obj = data;
    const result = {};
    // Only include properties that have valid string values
    if (obj.startDate && typeof obj.startDate === 'string' && obj.startDate.trim() !== '') {
        result.startDate = obj.startDate;
    }
    if (obj.endDate && typeof obj.endDate === 'string' && obj.endDate.trim() !== '') {
        result.endDate = obj.endDate;
    }
    // If no valid dates, return undefined to make the whole dateRange undefined
    if (!result.startDate && !result.endDate) {
        return undefined;
    }
    return result;
}, z.object({
    startDate: z.string().datetime('Invalid start date format').optional(),
    endDate: z.string().datetime('Invalid end date format').optional(),
}).refine((data) => {
    // If both dates are provided, validate the order
    if (data.startDate && data.endDate) {
        return new Date(data.startDate) <= new Date(data.endDate);
    }
    return true; // Allow partial date ranges
}, {
    message: 'Start date must be before or equal to end date',
    path: ['startDate'],
}).optional());
export const paginationSchema = z.object({
    page: z.number().int().min(1).optional(),
    per_page: z.number().int().min(1).max(150).optional(),
    starting_after: z.string().optional(),
});
export const sortSchema = z.object({
    field: z.string().min(1),
    order: z.enum(['asc', 'desc']).default('desc'),
});
// Tool-specific validation schemas
export const listConversationsSchema = z.object({
    dateRange: flexibleDateRangeSchema.optional(),
    adminIds: z.array(z.string()).optional(),
    tags: z.array(z.string()).optional(),
    status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
    hasRating: z.boolean().optional(),
    pagination: paginationSchema.optional(),
    sort: sortSchema.optional(),
});
export const analyzeConversationSchema = z.object({
    conversationId: conversationIdSchema,
    includeConversationParts: z.boolean().default(true),
    includeCustomerFeedback: z.boolean().default(true),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
    focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
});
export const getUnratedConversationsSchema = z.object({
    dateRange: flexibleDateRangeSchema.optional(),
    adminIds: z.array(z.string()).optional(),
    status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
    priority: z.array(z.enum(['high', 'normal', 'low'])).optional(),
    pagination: paginationSchema.optional(),
    sort: sortSchema.optional(),
    includePredictedSatisfaction: z.boolean().default(false),
});
export const analyzeSupportPerformanceSchema = z.object({
    adminId: z.string().min(1, 'Admin ID is required'),
    dateRange: dateRangeSchema,
    includeConversationExamples: z.boolean().default(true),
    maxExamples: z.number().int().min(1).max(10).default(5),
});
export const getConversationInsightsSchema = z.object({
    conversationId: conversationIdSchema,
    includeTopics: z.boolean().default(true),
    includeSentiment: z.boolean().default(true),
    includeActionItems: z.boolean().default(true),
});
export const bulkAnalyzeConversationsSchema = z.object({
    conversationIds: z.array(conversationIdSchema).min(1).max(50),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
    includeConversationParts: z.boolean().default(true),
    includeCustomerFeedback: z.boolean().default(true),
    focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
    generateSummary: z.boolean().default(true),
});
// Validation helper functions
export function validateInput(schema, input) {
    try {
        return schema.parse(input);
    }
    catch (error) {
        if (error instanceof z.ZodError) {
            const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Validation failed: ${errorMessages.join(', ')}`);
        }
        throw error;
    }
}
export function validateConversationId(id) {
    return validateInput(conversationIdSchema, id);
}
export function validateDateRange(range) {
    return validateInput(dateRangeSchema, range);
}
export function validatePagination(params) {
    return validateInput(paginationSchema, params);
}
// Custom validation functions
export function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
export function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
}
export function sanitizeString(input) {
    return input.trim().replace(/[<>]/g, '');
}
export function normalizeTag(tag) {
    return tag.toLowerCase().trim().replace(/\s+/g, '-');
}
// Rate limiting validation
export function validateRateLimit(remaining, resetTime) {
    if (remaining <= 0) {
        const resetDate = new Date(resetTime * 1000);
        const waitTime = Math.ceil((resetDate.getTime() - Date.now()) / 1000);
        throw new Error(`Rate limit exceeded. Please wait ${waitTime} seconds before retrying.`);
    }
}
// Data sanitization for OpenAI API
export function sanitizeForAnalysis(text) {
    // Remove potentially sensitive information
    return text
        .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
        .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, '[CARD]')
        .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]')
        .replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE]')
        .trim();
}
// Conversation content validation
export function validateConversationContent(content) {
    if (!content || content.trim().length === 0) {
        return false;
    }
    // Check for minimum meaningful content length
    if (content.trim().length < 10) {
        return false;
    }
    // Check for spam patterns (basic)
    const spamPatterns = [
        /^(.)\1{10,}$/, // Repeated characters
        /^[^a-zA-Z]*$/, // No letters
    ];
    return !spamPatterns.some(pattern => pattern.test(content));
}
//# sourceMappingURL=validation.js.map