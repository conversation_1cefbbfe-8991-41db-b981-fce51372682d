/**
 * Intercom API client with rate limiting and error handling
 */

import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import {  URLSearchParams } from 'url';

// Extend the Axios request config to include metadata
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  metadata?: {
    startTime: number;
  };
}
import { config } from '../utils/config.js';
import { log } from '../utils/logger.js';
import { validateRateLimit } from '../utils/validation.js';
import type {
  IntercomConversation,
  IntercomConversationList,
  IntercomSearchQuery,
  IntercomApiError,
  IntercomAdmin,
  RateLimitInfo,
  PaginationParams,
} from '../types/index.js';

export class IntercomApiClient {
  private client: AxiosInstance;
  private rateLimitInfo: RateLimitInfo = {
    limit: 1000,
    remaining: 1000,
    resetTime: Date.now() / 1000 + 3600,
  };

  constructor() {
    this.client = axios.create({
      baseURL: config.intercom.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.intercom.accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Intercom-Version': config.intercom.apiVersion,
      },
      timeout: 30000, // 30 seconds
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        log.apiRequest(config.method?.toUpperCase() || 'GET', config.url || '');
        return config;
      },
      (error) => {
        log.error('Request interceptor error', {}, error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for rate limiting and logging
    this.client.interceptors.response.use(
      (response) => {
        this.updateRateLimitInfo(response);
        log.apiResponse(
          response.config.method?.toUpperCase() || 'GET',
          response.config.url || '',
          response.status,
          Date.now() - ((response.config as ExtendedAxiosRequestConfig).metadata?.startTime || Date.now())
        );
        return response;
      },
      (error) => {
        if (error.response) {
          this.updateRateLimitInfo(error.response);
          log.error('API error response', {
            status: error.response.status,
            data: error.response.data,
          });
        } else {
          log.error('API request failed', {}, error);
        }
        return Promise.reject(this.handleApiError(error));
      }
    );

    // Add request timing metadata
    this.client.interceptors.request.use((config) => {
      (config as ExtendedAxiosRequestConfig).metadata = { startTime: Date.now() };
      return config;
    });
  }

  private updateRateLimitInfo(response: AxiosResponse): void {
    const headers = response.headers;
    if (headers['x-ratelimit-limit']) {
      this.rateLimitInfo.limit = parseInt(headers['x-ratelimit-limit'], 10);
    }
    if (headers['x-ratelimit-remaining']) {
      this.rateLimitInfo.remaining = parseInt(headers['x-ratelimit-remaining'], 10);
    }
    if (headers['x-ratelimit-reset']) {
      this.rateLimitInfo.resetTime = parseInt(headers['x-ratelimit-reset'], 10);
    }

    log.rateLimit(
      'intercom',
      this.rateLimitInfo.remaining,
      this.rateLimitInfo.resetTime
    );
  }

  private handleApiError(error: any): Error {
    if (error.response?.data?.errors) {
      const intercomError = error.response.data as IntercomApiError;
      const errorMessage = intercomError.errors
        .map(err => `${err.code}: ${err.message}`)
        .join(', ');
      return new Error(`Intercom API error: ${errorMessage}`);
    }

    if (error.response?.status === 429) {
      return new Error('Rate limit exceeded. Please try again later.');
    }

    if (error.response?.status === 401) {
      return new Error('Invalid Intercom access token. Please check your credentials.');
    }

    if (error.response?.status === 403) {
      return new Error('Insufficient permissions for this Intercom API operation.');
    }

    return new Error(`Intercom API request failed: ${error.message}`);
  }

  private checkRateLimit(): void {
    validateRateLimit(this.rateLimitInfo.remaining, this.rateLimitInfo.resetTime);
  }

  /**
   * List all conversations with optional filtering
   */
  async listConversations(params: PaginationParams = {}): Promise<IntercomConversationList> {
    this.checkRateLimit();

    const queryParams = new URLSearchParams();
    if (params.per_page) queryParams.append('per_page', params.per_page.toString());
    if (params.starting_after) queryParams.append('starting_after', params.starting_after);

    const response = await this.client.get<IntercomConversationList>(
      `/conversations?${queryParams.toString()}`
    );

    return response.data;
  }

  /**
   * Get a specific conversation by ID
   */
  async getConversation(conversationId: string): Promise<IntercomConversation> {
    this.checkRateLimit();

    const response = await this.client.get<IntercomConversation>(
      `/conversations/${conversationId}`
    );

    return response.data;
  }

  /**
   * Search conversations using Intercom's search API
   */
  async searchConversations(query: IntercomSearchQuery): Promise<IntercomConversationList> {
    this.checkRateLimit();

    const response = await this.client.post<IntercomConversationList>(
      '/conversations/search',
      query
    );

    return response.data;
  }

  /**
   * Get conversations without ratings
   */
  async getUnratedConversations(params: {
    dateRange?: { startDate: string; endDate: string };
    adminIds?: string[];
    status?: ('open' | 'closed' | 'snoozed')[];
    priority?: ('high' | 'normal' | 'low')[];
    pagination?: PaginationParams;
  } = {}): Promise<IntercomConversationList> {
    const query: IntercomSearchQuery = {
      query: {
        operator: 'AND',
        operands: [
          {
            field: 'conversation_rating.rating',
            operator: '=',
            value: 'NULL',
          },
        ],
      },
    };

    // Add date range filter
    if (params.dateRange) {
      query.query.operands.push({
        field: 'created_at',
        operator: '>',
        value: Math.floor(new Date(params.dateRange.startDate).getTime() / 1000),
      });
      query.query.operands.push({
        field: 'created_at',
        operator: '<',
        value: Math.floor(new Date(params.dateRange.endDate).getTime() / 1000),
      });
    }

    // Add admin filter
    if (params.adminIds && params.adminIds.length > 0) {
      query.query.operands.push({
        field: 'admin_assignee_id',
        operator: 'IN',
        value: params.adminIds.join(','),
      });
    }

    // Add status filter
    if (params.status && params.status.length > 0) {
      query.query.operands.push({
        field: 'state',
        operator: 'IN',
        value: params.status.join(','),
      });
    }

    // Add pagination
    if (params.pagination) {
      query.pagination = params.pagination;
    }

    return this.searchConversations(query);
  }

  /**
   * Get conversations by admin ID
   */
  async getConversationsByAdmin(
    adminId: string,
    params: {
      dateRange?: { startDate: string; endDate: string };
      pagination?: PaginationParams;
    } = {}
  ): Promise<IntercomConversationList> {
    const query: IntercomSearchQuery = {
      query: {
        operator: 'AND',
        operands: [
          {
            field: 'admin_assignee_id',
            operator: '=',
            value: adminId,
          },
        ],
      },
    };

    // Add date range filter
    if (params.dateRange) {
      query.query.operands.push({
        field: 'created_at',
        operator: '>',
        value: Math.floor(new Date(params.dateRange.startDate).getTime() / 1000),
      });
      query.query.operands.push({
        field: 'created_at',
        operator: '<',
        value: Math.floor(new Date(params.dateRange.endDate).getTime() / 1000),
      });
    }

    // Add pagination
    if (params.pagination) {
      query.pagination = params.pagination;
    }

    return this.searchConversations(query);
  }

  /**
   * Get all admins
   */
  async getAdmins(): Promise<IntercomAdmin[]> {
    this.checkRateLimit();

    const response = await this.client.get<{ type: string; admins: IntercomAdmin[] }>('/admins');
    return response.data.admins;
  }

  /**
   * Get admin by ID
   */
  async getAdmin(adminId: string): Promise<IntercomAdmin> {
    this.checkRateLimit();

    const response = await this.client.get<IntercomAdmin>(`/admins/${adminId}`);
    return response.data;
  }

  /**
   * Get current rate limit information
   */
  getRateLimitInfo(): RateLimitInfo {
    return { ...this.rateLimitInfo };
  }
}
