#!/usr/bin/env node

/**
 * Intercom MCP Server
 *
 * A Model Context Protocol server for analyzing Intercom conversations
 * and support quality using OpenAI.
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';

import { AnalysisService } from './services/analysis.js';
import { config } from './utils/config.js';
import { log } from './utils/logger.js';
import {
  validateInput,
  listConversationsSchema,
  analyzeConversationSchema,
  getUnratedConversationsSchema,
  analyzeSupportPerformanceSchema,
  getConversationInsightsSchema,
  bulkAnalyzeConversationsSchema,
} from './utils/validation.js';
import { AnalysisFilter, AnalysisConfig } from './types/analysis.js';
import { PaginationParams } from './types/index.js';

class IntercomMcpServer {
  private server: McpServer;
  private analysisService: AnalysisService;

  constructor() {
    this.server = new McpServer({
      name: 'intercom-analysis-server',
      version: '1.0.0',
    });

    this.analysisService = new AnalysisService();
    this.setupTools();
  }

  private setupTools(): void {
    // Tool 1: List conversations with filtering
    this.server.tool(
      'list-conversations',
      {
        dateRange: z.object({
          startDate: z.string().datetime(),
          endDate: z.string().datetime(),
        }).optional(),
        adminIds: z.array(z.string()).optional(),
        tags: z.array(z.string()).optional(),
        status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
        hasRating: z.boolean().optional(),
        pagination: z.object({
          page: z.number().int().min(1).optional(),
          per_page: z.number().int().min(1).max(150).optional(),
          starting_after: z.string().optional(),
        }).optional(),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(listConversationsSchema, params);

          // Build filter object with only defined properties
          const filter: Partial<AnalysisFilter> = {};
          if (validatedParams.dateRange && (validatedParams.dateRange.startDate || validatedParams.dateRange.endDate)) {
            filter.dateRange = validatedParams.dateRange;
          }
          if (validatedParams.adminIds) filter.adminIds = validatedParams.adminIds;
          if (validatedParams.tags) filter.tags = validatedParams.tags;
          if (validatedParams.status) filter.status = validatedParams.status;
          if (validatedParams.hasRating !== undefined) filter.hasRating = validatedParams.hasRating;

          // Build pagination object with only defined properties
          const pagination: Partial<PaginationParams> = {};
          if (validatedParams.pagination?.page) pagination.page = validatedParams.pagination.page;
          if (validatedParams.pagination?.per_page) pagination.per_page = validatedParams.pagination.per_page;
          if (validatedParams.pagination?.starting_after) pagination.starting_after = validatedParams.pagination.starting_after;

          const result = await this.analysisService.listConversations(
            filter,
            pagination
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                conversations: result.conversations.map(conv => ({
                  id: conv.id,
                  title: conv.title,
                  status: conv.state,
                  priority: conv.priority,
                  createdAt: new Date(conv.created_at * 1000).toISOString(),
                  updatedAt: new Date(conv.updated_at * 1000).toISOString(),
                  hasRating: !!conv.conversation_rating,
                  rating: conv.conversation_rating?.rating,
                  adminAssigneeId: conv.admin_assignee_id,
                  messageCount: conv.conversation_parts?.total_count || 0,
                  tags: conv.tags.tags.map(tag => tag.name),
                })),
                totalCount: result.totalCount,
              }, null, 2),
            }],
          };
        } catch (error) {
          log.error('list-conversations tool failed', {}, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );

    // Tool 2: Analyze conversation quality
    this.server.tool(
      'analyze-conversation-quality',
      {
        conversationId: z.string().min(1),
        includeConversationParts: z.boolean().default(true),
        includeCustomerFeedback: z.boolean().default(true),
        analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
        focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(analyzeConversationSchema, params);

          // Build config object with only defined properties
          const config: Partial<AnalysisConfig> = {
            includeConversationParts: validatedParams.includeConversationParts ?? true,
            includeCustomerFeedback: validatedParams.includeCustomerFeedback ?? true,
            analysisDepth: validatedParams.analysisDepth ?? 'detailed',
          };
          if (validatedParams.focusAreas) config.focusAreas = validatedParams.focusAreas;

          const analysis = await this.analysisService.analyzeConversationQuality(
            validatedParams.conversationId,
            config as AnalysisConfig
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify(analysis, null, 2),
            }],
          };
        } catch (error) {
          log.error('analyze-conversation-quality tool failed', { conversationId: params.conversationId }, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error analyzing conversation: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );

    // Tool 3: Get unrated conversations
    this.server.tool(
      'get-unrated-conversations',
      {
        dateRange: z.object({
          startDate: z.string().datetime(),
          endDate: z.string().datetime(),
        }).optional(),
        adminIds: z.array(z.string()).optional(),
        status: z.array(z.enum(['open', 'closed', 'snoozed'])).optional(),
        priority: z.array(z.enum(['high', 'normal', 'low'])).optional(),
        pagination: z.object({
          page: z.number().int().min(1).optional(),
          per_page: z.number().int().min(1).max(150).optional(),
          starting_after: z.string().optional(),
        }).optional(),
        includePredictedSatisfaction: z.boolean().default(false),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(getUnratedConversationsSchema, params);

          // Build filter object with only defined properties
          const filter: Partial<AnalysisFilter> = {};
          if (validatedParams.dateRange && (validatedParams.dateRange.startDate || validatedParams.dateRange.endDate)) {
            filter.dateRange = validatedParams.dateRange;
          }
          if (validatedParams.adminIds) filter.adminIds = validatedParams.adminIds;
          if (validatedParams.status) filter.status = validatedParams.status;
          if (validatedParams.priority) filter.priority = validatedParams.priority;

          // Build pagination object with only defined properties
          const pagination: Partial<PaginationParams> = {};
          if (validatedParams.pagination?.page) pagination.page = validatedParams.pagination.page;
          if (validatedParams.pagination?.per_page) pagination.per_page = validatedParams.pagination.per_page;
          if (validatedParams.pagination?.starting_after) pagination.starting_after = validatedParams.pagination.starting_after;

          const unratedConversations = await this.analysisService.getUnratedConversations(
            filter,
            pagination
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify({
                unratedConversations,
                count: unratedConversations.length,
                summary: {
                  byStatus: this.groupBy(unratedConversations, 'status'),
                  byPriority: this.groupBy(unratedConversations, 'priority'),
                  averageMessageCount: unratedConversations.reduce((sum, conv) => sum + conv.messageCount, 0) / unratedConversations.length || 0,
                },
              }, null, 2),
            }],
          };
        } catch (error) {
          log.error('get-unrated-conversations tool failed', {}, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error fetching unrated conversations: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );

    // Tool 4: Analyze support performance
    this.server.tool(
      'analyze-support-performance',
      {
        adminId: z.string().min(1),
        dateRange: z.object({
          startDate: z.string().datetime(),
          endDate: z.string().datetime(),
        }),
        includeConversationExamples: z.boolean().default(true),
        maxExamples: z.number().int().min(1).max(10).default(5),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(analyzeSupportPerformanceSchema, params);
          const performance = await this.analysisService.analyzeSupportPerformance(
            validatedParams.adminId,
            validatedParams.dateRange,
            {
              includeConversationExamples: validatedParams.includeConversationExamples ?? true,
              maxExamples: validatedParams.maxExamples ?? 5,
            }
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify(performance, null, 2),
            }],
          };
        } catch (error) {
          log.error('analyze-support-performance tool failed', { adminId: params.adminId }, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error analyzing support performance: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );

    // Tool 5: Get conversation insights
    this.server.tool(
      'get-conversation-insights',
      {
        conversationId: z.string().min(1),
        includeTopics: z.boolean().default(true),
        includeSentiment: z.boolean().default(true),
        includeActionItems: z.boolean().default(true),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(getConversationInsightsSchema, params);
          const insights = await this.analysisService.getConversationInsights(
            validatedParams.conversationId,
            {
              includeTopics: validatedParams.includeTopics ?? true,
              includeSentiment: validatedParams.includeSentiment ?? true,
              includeActionItems: validatedParams.includeActionItems ?? true,
            }
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify(insights, null, 2),
            }],
          };
        } catch (error) {
          log.error('get-conversation-insights tool failed', { conversationId: params.conversationId }, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error getting conversation insights: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );

    // Tool 6: Bulk analyze conversations
    this.server.tool(
      'bulk-analyze-conversations',
      {
        conversationIds: z.array(z.string().min(1)).min(1).max(50),
        analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
        includeConversationParts: z.boolean().default(true),
        includeCustomerFeedback: z.boolean().default(true),
        focusAreas: z.array(z.enum(['responsiveness', 'helpfulness', 'professionalism', 'resolution'])).optional(),
        generateSummary: z.boolean().default(true),
      },
      async (params) => {
        try {
          const validatedParams = validateInput(bulkAnalyzeConversationsSchema, params);

          // Build config object with only defined properties
          const config: Partial<AnalysisConfig> = {
            includeConversationParts: validatedParams.includeConversationParts ?? true,
            includeCustomerFeedback: validatedParams.includeCustomerFeedback ?? true,
            analysisDepth: validatedParams.analysisDepth ?? 'detailed',
          };
          if (validatedParams.focusAreas) config.focusAreas = validatedParams.focusAreas;

          const bulkResult = await this.analysisService.bulkAnalyzeConversations(
            validatedParams.conversationIds,
            config as AnalysisConfig
          );

          return {
            content: [{
              type: 'text',
              text: JSON.stringify(bulkResult, null, 2),
            }],
          };
        } catch (error) {
          log.error('bulk-analyze-conversations tool failed', { conversationIds: params.conversationIds }, error as Error);
          return {
            content: [{
              type: 'text',
              text: `Error performing bulk analysis: ${(error as Error).message}`,
            }],
            isError: true,
          };
        }
      }
    );
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((groups, item) => {
      const value = String(item[key]);
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {} as Record<string, number>);
  }

  async start(): Promise<void> {
    try {
      log.info('Starting Intercom MCP Server', {
        version: '1.0.0',
        nodeEnv: config.server.nodeEnv,
        logLevel: config.server.logLevel,
      });

      const transport = new StdioServerTransport();
      await this.server.connect(transport);

      log.info('Intercom MCP Server started successfully');
    } catch (error) {
      log.error('Failed to start Intercom MCP Server', {}, error as Error);
      process.exit(1);
    }
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new IntercomMcpServer();
  server.start().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}
