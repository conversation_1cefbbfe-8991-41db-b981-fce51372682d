# Intercom API Configuration
INTERCOM_ACCESS_TOKEN=************************************************************
INTERCOM_API_VERSION=2.13

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4o-mini

# Server Configuration
LOG_LEVEL=info
NODE_ENV=development

# Optional: Rate limiting and caching
RATE_LIMIT_REQUESTS_PER_MINUTE=60
CACHE_TTL_SECONDS=300